
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.autograd import grad
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
import argparse
import os
import random
import matplotlib.pyplot as plt
import scienceplots

# Check for GPU availability
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 0. Dataloader classes (copied from dataloader/dataloader.py for portability)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class DF():
    def __init__(self,args):
        self.normalization = True
        self.normalization_method = args.normalization_method
        self.args = args

    def _3_sigma(self, Ser1):
        rule = (Ser1.mean() - 3 * Ser1.std() > Ser1) | (Ser1.mean() + 3 * Ser1.std() < Ser1)
        index = np.arange(Ser1.shape[0])[rule]
        return index

    def delete_3_sigma(self,df):
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.dropna()
        df = df.reset_index(drop=True)
        out_index = []
        for col in df.columns:
            index = self._3_sigma(df[col])
            out_index.extend(index)
        out_index = list(set(out_index))
        df = df.drop(out_index, axis=0)
        df = df.reset_index(drop=True)
        return df

    def read_one_csv(self,file_name,nominal_capacity=None):
        df = pd.read_csv(file_name)
        df.insert(df.shape[1]-1,'cycle index',np.arange(df.shape[0], dtype=np.float64))
        df = self.delete_3_sigma(df)
        if nominal_capacity is not None:
            df['capacity'] = df['capacity']/nominal_capacity
            f_df = df.iloc[:,:-1]
            if self.normalization_method == 'min-max':
                f_df = 2*(f_df - f_df.min())/(f_df.max() - f_df.min()) - 1
            elif self.normalization_method == 'z-score':
                f_df = (f_df - f_df.mean())/f_df.std()
            df.iloc[:,:-1] = f_df
        return df

    def load_one_battery(self,path,nominal_capacity=None):
        df = self.read_one_csv(path,nominal_capacity)
        x = df.iloc[:,:-1].values
        y = df.iloc[:,-1].values
        x1 = x[:-1]
        x2 = x[1:]
        y1 = y[:-1]
        y2 = y[1:]
        return (x1,y1),(x2,y2)

    def load_all_battery(self,path_list,nominal_capacity):
        X1, X2, Y1, Y2 = [], [], [], []
        for path in path_list:
            (x1, y1), (x2, y2) = self.load_one_battery(path, nominal_capacity)
            X1.append(x1)
            X2.append(x2)
            Y1.append(y1)
            Y2.append(y2)

        X1 = np.concatenate(X1, axis=0)
        X2 = np.concatenate(X2, axis=0)
        Y1 = np.concatenate(Y1, axis=0)
        Y2 = np.concatenate(Y2, axis=0)

        tensor_X1 = torch.from_numpy(X1).float()
        tensor_X2 = torch.from_numpy(X2).float()
        tensor_Y1 = torch.from_numpy(Y1).float().view(-1,1)
        tensor_Y2 = torch.from_numpy(Y2).float().view(-1,1)
        
        split = int(tensor_X1.shape[0] * 0.8)
        train_X1, test_X1 = tensor_X1[:split], tensor_X1[split:]
        train_X2, test_X2 = tensor_X2[:split], tensor_X2[split:]
        train_Y1, test_Y1 = tensor_Y1[:split], tensor_Y1[split:]
        train_Y2, test_Y2 = tensor_Y2[:split], tensor_Y2[split:]
        
        train_X1, valid_X1, train_X2, valid_X2, train_Y1, valid_Y1, train_Y2, valid_Y2 = \
            train_test_split(train_X1, train_X2, train_Y1, train_Y2, test_size=0.2, random_state=420)

        train_loader = DataLoader(TensorDataset(train_X1, train_X2, train_Y1, train_Y2), batch_size=self.args.batch_size, shuffle=True)
        valid_loader = DataLoader(TensorDataset(valid_X1, valid_X2, valid_Y1, valid_Y2), batch_size=self.args.batch_size, shuffle=True)
        test_loader = DataLoader(TensorDataset(test_X1, test_X2, test_Y1, test_Y2), batch_size=self.args.batch_size, shuffle=False)

        loader = {'train': train_loader, 'valid': valid_loader, 'test': test_loader}
        return loader

class XJTUdata(DF):
    def __init__(self, root, args):
        super(XJTUdata, self).__init__(args)
        self.root = root
        self.file_list = os.listdir(root)
        self.batch_names = ['2C','3C','R2.5','R3','RW']
        self.nominal_capacity = 2.0

    def read_one_batch(self,batch='2C'):
        if isinstance(batch,int):
            batch = self.batch_names[batch]
        file_list = [os.path.join(self.root, f) for f in self.file_list if batch in f]
        return self.load_all_battery(path_list=file_list,nominal_capacity=self.nominal_capacity)

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 1. Fourier Neural Operator (FNO) Implementation
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class SpectralConv1d(nn.Module):
    def __init__(self, in_channels, out_channels, modes):
        super(SpectralConv1d, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.modes = modes
        self.scale = (1 / (in_channels * out_channels))
        self.weights = nn.Parameter(self.scale * torch.rand(in_channels, out_channels, self.modes, dtype=torch.cfloat))

    def forward(self, x):
        batch_size = x.shape[0]
        x_ft = torch.fft.rfft(x)
        out_ft = torch.zeros(batch_size, self.out_channels, x.size(-1)//2 + 1, device=x.device, dtype=torch.cfloat)
        out_ft[:, :, :self.modes] = torch.einsum("bix,iox->box", x_ft[:, :, :self.modes], self.weights)
        return torch.fft.irfft(out_ft, n=x.size(-1))

class FNO1d(nn.Module):
    def __init__(self, modes, width, input_channels=1, output_channels=1):
        super(FNO1d, self).__init__()
        self.width = width
        self.p = nn.Linear(input_channels, self.width)
        self.conv0 = SpectralConv1d(self.width, self.width, modes)
        self.w0 = nn.Conv1d(self.width, self.width, 1)
        self.act = nn.GELU()
        self.q = nn.Linear(self.width, output_channels)

    def forward(self, x):
        if len(x.shape) == 2:
            x = x.unsqueeze(-1)
        x = x.permute(0, 2, 1)
        x = self.p(x.permute(0, 2, 1))
        x = x.permute(0, 2, 1)
        x1 = self.conv0(x)
        x2 = self.w0(x)
        x = self.act(x1 + x2)
        x = x.permute(0, 2, 1)
        x = self.q(x)
        return x.mean(dim=1)

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 2. Helper classes and PINN Model
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class Sin(nn.Module):
    def forward(self, x):
        return torch.sin(x)

class MLP(nn.Module):
    def __init__(self,input_dim=17,output_dim=1,layers_num=4,hidden_dim=50,droupout=0.2):
        super(MLP, self).__init__()
        self.layers = []
        for i in range(layers_num):
            if i == 0:
                self.layers.append(nn.Linear(input_dim,hidden_dim))
                self.layers.append(Sin())
            elif i == layers_num-1:
                self.layers.append(nn.Linear(hidden_dim,output_dim))
            else:
                self.layers.append(nn.Linear(hidden_dim,hidden_dim))
                self.layers.append(Sin())
                self.layers.append(nn.Dropout(p=droupout))
        self.net = nn.Sequential(*self.layers)

    def forward(self,x):
        return self.net(x)

class Predictor(nn.Module):
    def __init__(self,input_dim=32):
        super(Predictor, self).__init__()
        self.net = nn.Sequential(nn.Dropout(p=0.2), nn.Linear(input_dim,32), Sin(), nn.Linear(32,1))
    def forward(self,x):
        return self.net(x)

class Solution_u(nn.Module):
    def __init__(self):
        super(Solution_u, self).__init__()
        self.encoder = MLP(input_dim=17,output_dim=32,layers_num=3,hidden_dim=60,droupout=0.2)
        self.predictor = Predictor(input_dim=32)
    def forward(self,x):
        return self.predictor(self.encoder(x))

class FNO_PINN(nn.Module):
    def __init__(self, args):
        super(FNO_PINN, self).__init__()
        self.args = args
        self.solution_u = Solution_u().to(device)
        self.dynamical_F = FNO1d(modes=args.fno_modes, width=args.fno_width, input_channels=1, output_channels=1).to(device)
        self.optimizer_u = torch.optim.Adam(self.solution_u.parameters(), lr=args.lr_u)
        self.optimizer_f = torch.optim.Adam(self.dynamical_F.parameters(), lr=args.lr_f)
        self.loss_func = nn.MSELoss()
        self.relu = nn.ReLU()
        self.alpha = self.args.alpha
        self.beta = self.args.beta

    def forward(self, xt):
        xt.requires_grad = True
        x = xt[:, :-1]
        t = xt[:, -1:]
        u = self.solution_u(torch.cat((x, t), dim=1))
        u_t = grad(u.sum(), t, create_graph=True, only_inputs=True)[0]
        u_x = grad(u.sum(), x, create_graph=True, only_inputs=True)[0]
        f_input = torch.cat([xt, u, u_x, u_t], dim=1)
        f_input_reshaped = f_input.unsqueeze(-1)
        F = self.dynamical_F(f_input_reshaped)
        f_residual = u_t - F
        return u, f_residual

    def train_one_epoch(self, dataloader):
        self.train()
        for x1, x2, y1, y2 in dataloader:
            x1, x2, y1, y2 = x1.to(device), x2.to(device), y1.to(device), y2.to(device)
            u1, f1 = self.forward(x1)
            u2, f2 = self.forward(x2)
            loss_data = 0.5 * self.loss_func(u1, y1) + 0.5 * self.loss_func(u2, y2)
            f_target = torch.zeros_like(f1)
            loss_pde = 0.5 * self.loss_func(f1, f_target) + 0.5 * self.loss_func(f2, f_target)
            loss_phys = self.relu(torch.mul(u2 - u1, y1 - y2)).mean()
            loss = loss_data + self.alpha * loss_pde + self.beta * loss_phys
            self.optimizer_u.zero_grad()
            self.optimizer_f.zero_grad()
            loss.backward()
            self.optimizer_u.step()
            self.optimizer_f.step()

    def evaluate(self, dataloader):
        self.eval()
        total_mse = 0
        with torch.no_grad():
            for x1, x2, y1, y2 in dataloader:
                x1, y1 = x1.to(device), y1.to(device)
                u1 = self.solution_u(x1)
                total_mse += self.loss_func(u1, y1).item()
        return total_mse / len(dataloader)

    def test(self, dataloader):
        self.eval()
        true_labels, pred_labels = [], []
        with torch.no_grad():
            for x1, x2, y1, y2 in dataloader:
                x1, y1 = x1.to(device), y1.to(device)
                u1 = self.solution_u(x1)
                true_labels.append(y1.cpu().numpy())
                pred_labels.append(u1.cpu().numpy())
        return np.concatenate(true_labels, axis=0), np.concatenate(pred_labels, axis=0)

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 3. Plotting Function
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def plot_results(true_labels, pred_labels, save_path):
    plt.style.use(['science', 'nature'])
    fig, ax = plt.subplots(figsize=(5, 3.5), dpi=200)
    ax.plot(true_labels, label='Ground Truth', linewidth=2, color='#403990')
    ax.plot(pred_labels, label='FNO-PINN Prediction', linestyle='--', linewidth=2, color='#CF3D3E')
    ax.set_xlabel('Sample Index (All Batches)')
    ax.set_ylabel('State of Health (SOH)')
    ax.set_title('FNO-PINN Prediction vs. Ground Truth')
    ax.legend(loc='best')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300)
    print(f"\n 종합 결과 플롯이 다음 경로에 저장되었습니다: {save_path}")

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 4. Main execution block
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def get_args():
    parser = argparse.ArgumentParser(description="Batch-run PINN with FNO for Battery SOH prediction")
    parser.add_argument('--data_root', type=str, default='./data/XJTU data', help='Root directory of the data')
    parser.add_argument('--epochs', type=int, default=50, help='Max epochs per batch') # Reduced for faster batch run
    parser.add_argument('--early_stop_patience', type=int, default=10, help='Patience for early stopping')
    # Fixed parameters for this script
    parser.add_argument('--normalization_method', type=str, default='z-score')
    parser.add_argument('--batch_size', type=int, default=256)
    parser.add_argument('--lr_u', type=float, default=1e-3)
    parser.add_argument('--lr_f', type=float, default=1e-3)
    parser.add_argument('--alpha', type=float, default=1.0)
    parser.add_argument('--beta', type=float, default=1.0)
    parser.add_argument('--fno_modes', type=int, default=12)
    parser.add_argument('--fno_width', type=int, default=32)
    return parser.parse_args()

if __name__ == "__main__":
    args = get_args()
    print("Device:", device)
    print("Arguments:", args)

    # --- Setup Directories and Batch List ---
    base_dir = './neural_operator_experiment'
    results_dir = os.path.join(base_dir, 'results')
    plots_dir = os.path.join(base_dir, 'plots')
    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(plots_dir, exist_ok=True)

    xjtu_batches = ['2C', '3C', 'R2.5', 'R3', 'RW']
    all_true_labels, all_pred_labels = [], []

    # --- Loop Over All Batches ---
    for batch_name in xjtu_batches:
        print(f"\n{ '='*20 } BATCH: {batch_name} { '='*20 }")
        
        # 1. Initialize Dataloader for the current batch
        print(f"--- Initializing Dataloader for batch {batch_name} ---")
        data_loader_class = XJTUdata(root=args.data_root, args=args)
        loaders = data_loader_class.read_one_batch(batch=batch_name)
        train_loader, valid_loader, test_loader = loaders['train'], loaders['valid'], loaders['test']
        print(f"Data loaded. Batches -> Train: {len(train_loader)}, Valid: {len(valid_loader)}, Test: {len(test_loader)}")

        # 2. Initialize a new Model for each batch
        model = FNO_PINN(args).to(device)
        model_save_path = os.path.join(results_dir, f'best_fno_model_{batch_name}.pth')

        # 3. Training Loop with Early Stopping
        print(f"--- Starting training for batch {batch_name} ---")
        min_valid_mse = float('inf')
        patience_counter = 0

        for epoch in range(1, args.epochs + 1):
            model.train_one_epoch(train_loader)
            valid_mse = model.evaluate(valid_loader)
            print(f"Epoch {epoch}/{args.epochs} | Validation MSE: {valid_mse:.6f}")

            if valid_mse < min_valid_mse:
                min_valid_mse = valid_mse
                patience_counter = 0
                torch.save(model.state_dict(), model_save_path)
            else:
                patience_counter += 1

            if patience_counter >= args.early_stop_patience:
                print(f"Early stopping triggered at epoch {epoch}.")
                break
        
        print(f"--- Training finished for batch {batch_name} ---")

        # 4. Test with the best model for the batch
        print(f"--- Testing with best model from {model_save_path} ---")
        model.load_state_dict(torch.load(model_save_path))
        true_labels, pred_labels = model.test(test_loader)
        all_true_labels.append(true_labels)
        all_pred_labels.append(pred_labels)

    # --- Aggregate and Save Final Results ---
    print(f"\n{ '='*20 } Aggregating All Results { '='*20 }")
    final_true = np.concatenate(all_true_labels, axis=0)
    final_pred = np.concatenate(all_pred_labels, axis=0)

    true_save_path = os.path.join(results_dir, "true_labels_all.npy")
    pred_save_path = os.path.join(results_dir, "pred_labels_all.npy")
    np.save(true_save_path, final_true)
    np.save(pred_save_path, final_pred)
    print(f"Aggregated test results saved to {results_dir}")

    # --- Plot Final Aggregated Results ---
    plot_save_path = os.path.join(plots_dir, 'prediction_plot_all_batches.png')
    plot_results(final_true, final_pred, save_path=plot_save_path)
