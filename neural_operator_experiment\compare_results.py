#!/usr/bin/env python3
"""
Compare results between different methods: LNO-BNN-PINN vs DeepONet-BNN-PINN
"""

import numpy as np
import matplotlib.pyplot as plt
import scienceplots
import os
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

def load_results(results_dir):
    """Load prediction results from saved files"""
    results = {}
    
    # Load LNO-BNN-PINN results
    try:
        results['lno_true'] = np.load(os.path.join(results_dir, 'true_labels_all_lno_bnn.npy'))
        results['lno_pred_mean'] = np.load(os.path.join(results_dir, 'pred_mean_all_lno_bnn.npy'))
        results['lno_pred_std'] = np.load(os.path.join(results_dir, 'pred_std_all_lno_bnn.npy'))
        print("✓ LNO-BNN-PINN results loaded")
    except FileNotFoundError:
        print("✗ LNO-BNN-PINN results not found")
        results['lno_true'] = None
    
    # Load DeepONet-BNN-PINN results
    try:
        results['deeponet_true'] = np.load(os.path.join(results_dir, 'true_labels_all_deeponet_bnn.npy'))
        results['deeponet_pred_mean'] = np.load(os.path.join(results_dir, 'pred_mean_all_deeponet_bnn.npy'))
        results['deeponet_pred_std'] = np.load(os.path.join(results_dir, 'pred_std_all_deeponet_bnn.npy'))
        print("✓ DeepONet-BNN-PINN results loaded")
    except FileNotFoundError:
        print("✗ DeepONet-BNN-PINN results not found")
        results['deeponet_true'] = None
    
    return results

def calculate_metrics(true, pred):
    """Calculate performance metrics"""
    mse = mean_squared_error(true, pred)
    mae = mean_absolute_error(true, pred)
    r2 = r2_score(true, pred)
    rmse = np.sqrt(mse)
    
    return {
        'MSE': mse,
        'MAE': mae,
        'RMSE': rmse,
        'R2': r2
    }

def plot_comparison(results, save_path):
    """Create comparison plot"""
    plt.style.use(['science', 'nature'])
    fig, axes = plt.subplots(2, 2, figsize=(12, 8), dpi=200)
    
    # Plot 1: LNO-BNN-PINN
    if results['lno_true'] is not None:
        ax = axes[0, 0]
        ax.plot(results['lno_true'], label='Ground Truth', color='#403990', zorder=3)
        ax.plot(results['lno_pred_mean'], label='LNO-BNN-PINN Prediction', color='#CF3D3E', zorder=2)
        ax.fill_between(range(len(results['lno_pred_mean'])), 
                        (results['lno_pred_mean'] - 2*results['lno_pred_std']).flatten(), 
                        (results['lno_pred_mean'] + 2*results['lno_pred_std']).flatten(), 
                        color='#FBDD85', alpha=0.5, label='95% CI', zorder=1)
        ax.set_title('LNO-BNN-PINN')
        ax.set_xlabel('Sample Index')
        ax.set_ylabel('SOH')
        ax.legend()
    
    # Plot 2: DeepONet-BNN-PINN
    if results['deeponet_true'] is not None:
        ax = axes[0, 1]
        ax.plot(results['deeponet_true'], label='Ground Truth', color='#403990', zorder=3)
        ax.plot(results['deeponet_pred_mean'], label='DeepONet-BNN-PINN Prediction', color='#CF3D3E', zorder=2)
        ax.fill_between(range(len(results['deeponet_pred_mean'])), 
                        (results['deeponet_pred_mean'] - 2*results['deeponet_pred_std']).flatten(), 
                        (results['deeponet_pred_mean'] + 2*results['deeponet_pred_std']).flatten(), 
                        color='#FBDD85', alpha=0.5, label='95% CI', zorder=1)
        ax.set_title('DeepONet-BNN-PINN')
        ax.set_xlabel('Sample Index')
        ax.set_ylabel('SOH')
        ax.legend()
    
    # Plot 3: Direct comparison
    if results['lno_true'] is not None and results['deeponet_true'] is not None:
        ax = axes[1, 0]
        ax.plot(results['lno_true'], label='Ground Truth', color='#403990', linewidth=2, zorder=3)
        ax.plot(results['lno_pred_mean'], label='LNO-BNN-PINN', color='#CF3D3E', linestyle='--', zorder=2)
        ax.plot(results['deeponet_pred_mean'], label='DeepONet-BNN-PINN', color='#2E8B57', linestyle=':', zorder=1)
        ax.set_title('Method Comparison')
        ax.set_xlabel('Sample Index')
        ax.set_ylabel('SOH')
        ax.legend()
    
    # Plot 4: Uncertainty comparison
    if results['lno_true'] is not None and results['deeponet_true'] is not None:
        ax = axes[1, 1]
        ax.plot(results['lno_pred_std'].flatten(), label='LNO-BNN-PINN Uncertainty', color='#CF3D3E')
        ax.plot(results['deeponet_pred_std'].flatten(), label='DeepONet-BNN-PINN Uncertainty', color='#2E8B57')
        ax.set_title('Uncertainty Comparison')
        ax.set_xlabel('Sample Index')
        ax.set_ylabel('Prediction Std')
        ax.legend()
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Comparison plot saved to: {save_path}")

def main():
    results_dir = './neural_operator_experiment/results'
    plots_dir = './neural_operator_experiment/plots'
    
    print("Loading results...")
    results = load_results(results_dir)
    
    print("\nCalculating metrics...")
    
    # Calculate metrics for LNO-BNN-PINN
    if results['lno_true'] is not None:
        lno_metrics = calculate_metrics(results['lno_true'], results['lno_pred_mean'])
        print("\nLNO-BNN-PINN Metrics:")
        for metric, value in lno_metrics.items():
            print(f"  {metric}: {value:.6f}")
    
    # Calculate metrics for DeepONet-BNN-PINN
    if results['deeponet_true'] is not None:
        deeponet_metrics = calculate_metrics(results['deeponet_true'], results['deeponet_pred_mean'])
        print("\nDeepONet-BNN-PINN Metrics:")
        for metric, value in deeponet_metrics.items():
            print(f"  {metric}: {value:.6f}")
    
    # Compare metrics
    if results['lno_true'] is not None and results['deeponet_true'] is not None:
        print("\nMetric Comparison (DeepONet vs LNO):")
        for metric in lno_metrics.keys():
            lno_val = lno_metrics[metric]
            deeponet_val = deeponet_metrics[metric]
            if metric == 'R2':  # Higher is better for R2
                improvement = ((deeponet_val - lno_val) / abs(lno_val)) * 100
            else:  # Lower is better for MSE, MAE, RMSE
                improvement = ((lno_val - deeponet_val) / abs(lno_val)) * 100
            print(f"  {metric}: {improvement:+.2f}% {'improvement' if improvement > 0 else 'degradation'}")
    
    # Create comparison plot
    comparison_plot_path = os.path.join(plots_dir, 'method_comparison.png')
    plot_comparison(results, comparison_plot_path)
    
    print(f"\nAnalysis completed!")

if __name__ == "__main__":
    main()
