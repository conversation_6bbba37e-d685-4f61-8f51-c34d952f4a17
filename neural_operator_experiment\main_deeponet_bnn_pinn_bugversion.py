import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.autograd import grad
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
import argparse
import os
import random
import matplotlib.pyplot as plt
import scienceplots

# BNN Imports from Blitz
import blitz.modules as bnn

# Check for GPU availability
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 0. Dataloader classes (copied for portability)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class DF():
    def __init__(self,args):
        self.normalization = True
        self.normalization_method = args.normalization_method
        self.args = args

    def _3_sigma(self, Ser1):
        rule = (Ser1.mean() - 3 * Ser1.std() > Ser1) | (Ser1.mean() + 3 * Ser1.std() < Ser1)
        return np.arange(Ser1.shape[0])[rule]

    def delete_3_sigma(self,df):
        df = df.replace([np.inf, -np.inf], np.nan).dropna().reset_index(drop=True)
        out_index = list(set([i for col in df.columns for i in self._3_sigma(df[col])]))
        return df.drop(out_index, axis=0).reset_index(drop=True)

    def read_one_csv(self,file_name,nominal_capacity=None):
        df = pd.read_csv(file_name)
        df.insert(df.shape[1]-1,'cycle index',np.arange(df.shape[0], dtype=np.float64))
        df = self.delete_3_sigma(df)
        if nominal_capacity is not None:
            df['capacity'] = df['capacity']/nominal_capacity
            f_df = df.iloc[:,:-1]
            if self.normalization_method == 'z-score':
                f_df = (f_df - f_df.mean())/f_df.std()
            df.iloc[:,:-1] = f_df
        return df

    def load_one_battery(self,path,nominal_capacity=None):
        df = self.read_one_csv(path,nominal_capacity)
        x, y = df.iloc[:,:-1].values, df.iloc[:,-1].values
        return (x[:-1],y[:-1]),(x[1:],y[1:])

    def load_all_battery(self,path_list,nominal_capacity):
        X1, X2, Y1, Y2 = [], [], [], []
        for path in path_list:
            (x1, y1), (x2, y2) = self.load_one_battery(path, nominal_capacity)
            X1.append(x1); X2.append(x2); Y1.append(y1); Y2.append(y2)

        X1, X2, Y1, Y2 = [np.concatenate(d, axis=0) for d in [X1, X2, Y1, Y2]]
        tensor_X1, tensor_X2, tensor_Y1, tensor_Y2 = [torch.from_numpy(d).float() for d in [X1, X2, Y1, Y2]]
        tensor_Y1, tensor_Y2 = tensor_Y1.view(-1,1), tensor_Y2.view(-1,1)
        
        split = int(tensor_X1.shape[0] * 0.8)
        train_X1, test_X1 = tensor_X1[:split], tensor_X1[split:]
        train_X2, test_X2 = tensor_X2[:split], tensor_X2[split:]
        train_Y1, test_Y1 = tensor_Y1[:split], tensor_Y1[split:]
        train_Y2, test_Y2 = tensor_Y2[:split], tensor_Y2[split:]
        
        train_X1, valid_X1, train_X2, valid_X2, train_Y1, valid_Y1, train_Y2, valid_Y2 = \
            train_test_split(train_X1, train_X2, train_Y1, train_Y2, test_size=0.2, random_state=420)

        train_loader = DataLoader(TensorDataset(train_X1, train_X2, train_Y1, train_Y2), batch_size=self.args.batch_size, shuffle=True)
        valid_loader = DataLoader(TensorDataset(valid_X1, valid_X2, valid_Y1, valid_Y2), batch_size=self.args.batch_size, shuffle=True)
        test_loader = DataLoader(TensorDataset(test_X1, test_X2, test_Y1, test_Y2), batch_size=self.args.batch_size, shuffle=False)

        return {'train': train_loader, 'valid': valid_loader, 'test': test_loader}

class XJTUdata(DF):
    def __init__(self, root, args):
        super(XJTUdata, self).__init__(args)
        self.root = root
        self.file_list = os.listdir(root)
        self.batch_names = ['2C','3C','R2.5','R3','RW']
        self.nominal_capacity = 2.0

    def read_one_batch(self,batch='2C'):
        file_list = [os.path.join(self.root, f) for f in self.file_list if batch in f]
        return self.load_all_battery(path_list=file_list,nominal_capacity=self.nominal_capacity)

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 1. DeepONet Implementation
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class DeepONet(nn.Module):
    def __init__(self, branch_input_dim, trunk_input_dim, output_dim, hidden_dim=100, num_layers=3):
        super(DeepONet, self).__init__()
        
        # Branch network
        branch_layers = []
        branch_layers.append(nn.Linear(branch_input_dim, hidden_dim))
        branch_layers.append(nn.GELU())
        for _ in range(num_layers - 2):
            branch_layers.append(nn.Linear(hidden_dim, hidden_dim))
            branch_layers.append(nn.GELU())
        branch_layers.append(nn.Linear(hidden_dim, output_dim)) # Output dimension for branch
        self.branch_net = nn.Sequential(*branch_layers)

        # Trunk network
        trunk_layers = []
        trunk_layers.append(nn.Linear(trunk_input_dim, hidden_dim))
        trunk_layers.append(nn.GELU())
        for _ in range(num_layers - 2):
            trunk_layers.append(nn.Linear(hidden_dim, hidden_dim))
            trunk_layers.append(nn.GELU())
        trunk_layers.append(nn.Linear(hidden_dim, output_dim)) # Output dimension for trunk
        self.trunk_net = nn.Sequential(*trunk_layers)

    def forward(self, branch_input, trunk_input):
        branch_output = self.branch_net(branch_input)
        trunk_output = self.trunk_net(trunk_input)
        
        output = torch.sum(branch_output * trunk_output, dim=-1, keepdim=True)
        return output

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 2. Bayesian Solution Network and Main PINN Model
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def get_kl_divergence(module):
    """Recursive function to fetch kl-divergence from Bayesian Modules"""
    kl_loss = torch.tensor(0.0, device=device)
    for sub_module in module.children():
        if isinstance(sub_module, bnn.BayesianLinear):
            kl_loss += sub_module.log_variational_posterior - sub_module.log_prior
        elif isinstance(sub_module, nn.Module):
            kl_loss += get_kl_divergence(sub_module)
    return kl_loss

class BayesianMLP(nn.Module):
    def __init__(self, input_dim, output_dim, hidden_dim, layers_num):
        super().__init__()
        self.layers = nn.ModuleList()
        for i in range(layers_num):
            in_features = input_dim if i == 0 else hidden_dim
            out_features = output_dim if i == layers_num - 1 else hidden_dim
            self.layers.append(bnn.BayesianLinear(in_features, out_features))
            if i < layers_num - 1:
                self.layers.append(nn.GELU())

    def forward(self, x):
        for layer in self.layers:
            x = layer(x)
        return x

class BayesianSolution_u(nn.Module):
    def __init__(self):
        super().__init__()
        self.encoder = BayesianMLP(input_dim=17, output_dim=32, hidden_dim=60, layers_num=3)
        self.predictor = BayesianMLP(input_dim=32, output_dim=1, hidden_dim=32, layers_num=2)
    
    def forward(self, x):
        return self.predictor(self.encoder(x))

class DeepONet_BNN_PINN(nn.Module):
    def __init__(self, args):
        super().__init__()
        self.args = args
        self.solution_u = BayesianSolution_u().to(device)
        # DeepONet expects branch_input_dim and trunk_input_dim
        # branch_input_dim: xt (2) + u (1) + u_x (1) + u_t (1) = 5
        # trunk_input_dim: xt (2)
        self.dynamical_F = DeepONet(branch_input_dim=20, trunk_input_dim=2, output_dim=args.deeponet_output_dim).to(device)
        self.optimizer_u = torch.optim.Adam(self.solution_u.parameters(), lr=args.lr_u)
        self.optimizer_f = torch.optim.Adam(self.dynamical_F.parameters(), lr=args.lr_f)
        self.loss_func = nn.MSELoss()
        self.relu = nn.ReLU()

    def forward(self, xt):
        xt.requires_grad = True
        x, t = xt[:, :-1], xt[:, -1:]
        u = self.solution_u(torch.cat((x, t), dim=1))
        u_t = grad(u.sum(), t, create_graph=True, only_inputs=True)[0]
        u_x = grad(u.sum(), x, create_graph=True, only_inputs=True)[0]
        
        # Prepare inputs for DeepONet
        branch_input = torch.cat([xt, u, u_x, u_t], dim=1) # Shape: [batch_size, 5]
        trunk_input = xt # Shape: [batch_size, 2]
        
        F = self.dynamical_F(branch_input, trunk_input)
        return u, u_t - F

    def train_one_epoch(self, dataloader):
        self.train()
        for x1, x2, y1, y2 in dataloader:
            x1, x2, y1, y2 = [d.to(device) for d in [x1, x2, y1, y2]]
            
            u1, f1 = self.forward(x1)
            u2, f2 = self.forward(x2)

            loss_data = 0.5 * self.loss_func(u1, y1) + 0.5 * self.loss_func(u2, y2)
            loss_pde = 0.5 * self.loss_func(f1, torch.zeros_like(f1)) + 0.5 * self.loss_func(f2, torch.zeros_like(f2))
            loss_phys = self.relu(torch.mul(u2 - u1, y1 - y2)).mean()
            
            complexity_cost = get_kl_divergence(self.solution_u)

            loss = loss_data + self.args.alpha * loss_pde + self.args.beta * loss_phys + self.args.complexity_weight * complexity_cost

            self.optimizer_u.zero_grad()
            self.optimizer_f.zero_grad()
            loss.backward()
            self.optimizer_u.step()
            self.optimizer_f.step()

    def predict_with_uncertainty(self, dataloader, n_samples):
        self.eval()
        all_true, all_preds = [], []
        with torch.no_grad():
            for x1, _, y1, _ in dataloader:
                x1, y1 = x1.to(device), y1.to(device)
                preds = [self.solution_u(x1).cpu().numpy() for _ in range(n_samples)]
                all_preds.append(np.stack(preds, axis=0))
                all_true.append(y1.cpu().numpy())
        
        all_preds = np.concatenate(all_preds, axis=1)
        pred_mean = np.mean(all_preds, axis=0)
        pred_std = np.std(all_preds, axis=0)
        return np.concatenate(all_true, axis=0), pred_mean, pred_std

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 3. Plotting Function
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def plot_with_uncertainty(true, pred_mean, pred_std, save_path):
    plt.style.use(['science', 'nature'])
    fig, ax = plt.subplots(figsize=(6, 4), dpi=200)
    
    ax.plot(true, label='Ground Truth', color='#403990', zorder=3)
    ax.plot(pred_mean, label='BNN-DeepONet-PINN Mean Prediction', color='#CF3D3E', zorder=2)
    
    ax.fill_between(range(len(pred_mean)), 
                    (pred_mean - 2*pred_std).flatten(), 
                    (pred_mean + 2*pred_std).flatten(), 
                    color='#FBDD85', alpha=0.5, label='95% Confidence Interval', zorder=1)

    ax.set_xlabel('Sample Index (All Batches)')
    ax.set_ylabel('State of Health (SOH)')
    ax.set_title('Prediction with Uncertainty vs. Ground Truth')
    ax.legend(loc='best')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300)
    print(f"\nPlot with uncertainty saved to: {save_path}")

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 4. Main execution block
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def get_args():
    parser = argparse.ArgumentParser(description="Batch-run LNO-BNN-PINN for Battery SOH")
    parser.add_argument('--data_root', type=str, default='../data/XJTU data')
    parser.add_argument('--epochs', type=int, default=75)
    parser.add_argument('--complexity_weight', type=float, default=1e-5, help='Weight for BNN complexity cost')
    parser.add_argument('--n_samples', type=int, default=50, help='Number of samples for uncertainty estimation')
    # Fixed parameters
    parser.add_argument('--normalization_method', type=str, default='z-score')
    parser.add_argument('--batch_size', type=int, default=256)
    parser.add_argument('--lr_u', type=float, default=1e-3)
    parser.add_argument('--lr_f', type=float, default=1e-3)
    parser.add_argument('--alpha', type=float, default=1.0)
    parser.add_argument('--beta', type=float, default=1.0)
    parser.add_argument('--deeponet_output_dim', type=int, default=128, help='Output dimension for DeepONet branch and trunk networks')
    return parser.parse_args()

if __name__ == "__main__":
    args = get_args()
    print("Device:", device, "\nArguments:", args)

    base_dir = './neural_operator_experiment'
    results_dir = os.path.join(base_dir, 'results')
    plots_dir = os.path.join(base_dir, 'plots')
    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(plots_dir, exist_ok=True)

    xjtu_batches = ['2C', '3C', 'R2.5', 'R3', 'RW']
    all_true, all_pred_mean, all_pred_std = [], [], []

    for batch_name in xjtu_batches:
        print(f"\n{'='*20} BATCH: {batch_name} {'='*20}")
        data_loader_class = XJTUdata(root=args.data_root, args=args)
        loaders = data_loader_class.read_one_batch(batch=batch_name)
        train_loader, valid_loader, test_loader = loaders['train'], loaders['valid'], loaders['test']
        
        model = DeepONet_BNN_PINN(args).to(device)
        model_save_path = os.path.join(results_dir, f'best_deeponet_bnn_model_{batch_name}.pth')

        print(f"--- Starting training for batch {batch_name} ---")
        for epoch in range(1, args.epochs + 1):
            model.train_one_epoch(train_loader)
            if epoch % 10 == 0:
                 print(f"Epoch {epoch}/{args.epochs} completed.")

        torch.save(model.state_dict(), model_save_path)
        print(f"--- Training finished. Saved final model to {model_save_path} ---")

        print(f"--- Testing with uncertainty quantification ---")
        true_labels, pred_mean, pred_std = model.predict_with_uncertainty(test_loader, n_samples=args.n_samples)
        all_true.append(true_labels)
        all_pred_mean.append(pred_mean)
        all_pred_std.append(pred_std)

    print(f"\n{'='*20} Aggregating All Results {'='*20}")
    final_true = np.concatenate(all_true, axis=0)
    final_pred_mean = np.concatenate(all_pred_mean, axis=0)
    final_pred_std = np.concatenate(all_pred_std, axis=0)

    np.save(os.path.join(results_dir, "true_labels_all_deeponet_bnn.npy"), final_true)
    np.save(os.path.join(results_dir, "pred_mean_all_deeponet_bnn.npy"), final_pred_mean)
    np.save(os.path.join(results_dir, "pred_std_all_deeponet_bnn.npy"), final_pred_std)
    print(f"Aggregated test results with uncertainty saved to {results_dir}")

    plot_save_path = os.path.join(plots_dir, 'prediction_plot_all_batches_deeponet_bnn.png')
    plot_with_uncertainty(final_true, final_pred_mean, final_pred_std, save_path=plot_save_path)