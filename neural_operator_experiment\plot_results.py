
import numpy as np
import matplotlib.pyplot as plt
import scienceplots
import os

# Set the plot style for a scientific publication look
plt.style.use(['science', 'nature'])

# Define paths
results_dir = './neural_operator_experiment/results'
plots_dir = './neural_operator_experiment/plots'
true_labels_path = os.path.join(results_dir, 'true_labels.npy')
pred_labels_path = os.path.join(results_dir, 'pred_labels.npy')
save_path = os.path.join(plots_dir, 'prediction_plot.png')

# Create plots directory if it doesn't exist
os.makedirs(plots_dir, exist_ok=True)

# Load the results
print(f"Loading results from {results_dir}...")
try:
    true_labels = np.load(true_labels_path)
    pred_labels = np.load(pred_labels_path)
except FileNotFoundError:
    print("Error: Results files not found.")
    print(f"Please run the training script 'main_fno_pinn.py' first to generate the results.")
    exit()

print("Results loaded successfully.")

# Create the plot
print("Generating plot...")
fig, ax = plt.subplots(figsize=(5, 3.5), dpi=200)

# Plot ground truth and predictions
# Since the test set can contain multiple batteries concatenated, we plot points instead of a continuous line
# to avoid misleading connections between different batteries.
ax.plot(true_labels, label='Ground Truth', linewidth=2, color='#403990')
ax.plot(pred_labels, label='FNO-PINN Prediction', linestyle='--', linewidth=2, color='#CF3D3E')

# Adding labels and title
ax.set_xlabel('Sample Index (Test Set)')
ax.set_ylabel('State of Health (SOH)')
ax.set_title('FNO-PINN Prediction vs. Ground Truth')

# Add legend
ax.legend(loc='best')

# Improve layout
plt.tight_layout()

# Save the figure
plt.savefig(save_path, dpi=300)
print(f"Plot saved successfully to {save_path}")

# Optionally, display the plot
# plt.show()
