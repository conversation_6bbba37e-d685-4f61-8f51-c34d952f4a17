name: latent_don
channels:
  - conda-forge
  - defaults
dependencies:
  - atk-1.0=2.36.0=he69c4ee_4
  - bzip2=1.0.8=h0d85af4_4
  - ca-certificates=2022.6.15=h033912b_0
  - cairo=1.16.0=h904041c_1013
  - expat=2.4.8=h96cf925_0
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=hab24e00_0
  - fontconfig=2.14.0=h676cef8_0
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - freetype=2.12.1=h3f81eb7_0
  - fribidi=1.0.10=hbcb3906_0
  - gdk-pixbuf=2.42.8=hb161b9c_0
  - gettext=********=hd1a6beb_1008
  - giflib=5.2.1=hbcb3906_2
  - graphite2=1.3.13=h2e338ed_1001
  - graphviz=5.0.1=ha8464fc_0
  - gtk2=2.24.33=h7c1209e_2
  - gts=0.7.6=hccb3bdf_2
  - harfbuzz=5.1.0=h00bb2c2_0
  - icu=70.1=h96cf925_0
  - jpeg=9e=hac89ed1_2
  - lerc=4.0.0=hb486fe8_0
  - libcxx=14.0.6=hccf4f1f_0
  - libdeflate=1.13=h775f41a_0
  - libffi=3.4.2=h0d85af4_5
  - libgd=2.3.3=h1e214de_3
  - libglib=2.72.1=hfbcb929_0
  - libiconv=1.16=haf1e3a3_0
  - libpng=1.6.37=h5481273_4
  - librsvg=2.54.4=h3d48ba6_0
  - libsqlite=3.39.3=ha978bb4_0
  - libtiff=4.4.0=h5e0c7b4_3
  - libtool=2.4.6=he49afe7_1008
  - libwebp=1.2.4=hfa4350a_0
  - libwebp-base=1.2.4=h775f41a_0
  - libxml2=2.9.14=hea49891_4
  - libzlib=1.2.12=hfe4f2af_2
  - ncurses=6.3=h96cf925_1
  - openssl=3.0.5=hb81d4ab_1
  - pango=1.50.9=hc8ec20f_0
  - pcre=8.45=he49afe7_0
  - pip=22.2.2=pyhd8ed1ab_0
  - pixman=0.40.0=hbcb3906_0
  - python=3.8.13=h66c20e1_0_cpython
  - readline=8.1.2=h3899abd_0
  - setuptools=65.3.0=pyhd8ed1ab_1
  - sqlite=3.39.3=h9ae0607_0
  - tk=8.6.12=h5dbffcc_0
  - wheel=0.37.1=pyhd8ed1ab_0
  - xz=5.2.6=h775f41a_0
  - zlib=1.2.12=hfe4f2af_2
  - zstd=1.5.2=hfa58983_4
  - pip:
    - absl-py==1.2.0
    - alabaster==0.7.12
    - applaunchservices==0.3.0
    - appnope==0.1.3
    - argon2-cffi==21.3.0
    - argon2-cffi-bindings==21.2.0
    - arrow==1.2.3
    - astroid==2.12.9
    - asttokens==2.0.8
    - astunparse==1.6.3
    - atomicwrites==1.4.1
    - attrs==22.1.0
    - autopep8==1.6.0
    - babel==2.10.3
    - backcall==0.2.0
    - beautifulsoup4==4.11.1
    - binaryornot==0.4.4
    - black==22.8.0
    - bleach==5.0.1
    - cachetools==5.2.0
    - certifi==2022.6.15
    - cffi==1.15.1
    - chardet==5.0.0
    - charset-normalizer==2.1.1
    - click==8.1.3
    - cloudpickle==2.2.0
    - cookiecutter==2.1.1
    - cycler==0.11.0
    - debugpy==1.6.3
    - decorator==5.1.1
    - defusedxml==0.7.1
    - diff-match-patch==20200713
    - dill==*******
    - dm-tree==0.1.7
    - docutils==0.19
    - entrypoints==0.4
    - executing==1.0.0
    - fastjsonschema==2.16.1
    - flake8==4.0.1
    - flatbuffers==2.0.7
    - fonttools==4.37.1
    - gast==0.3.3
    - google-auth==2.11.0
    - google-auth-oauthlib==0.4.6
    - google-pasta==0.2.0
    - grpcio==1.32.0
    - h5py==2.10.0
    - idna==3.3
    - imagesize==1.4.1
    - importlib-metadata==4.12.0
    - importlib-resources==5.9.0
    - inflection==0.5.1
    - intervaltree==3.1.0
    - ipykernel==6.15.2
    - ipython==7.34.0
    - ipython-genutils==0.2.0
    - ipywidgets==8.0.2
    - isort==5.10.1
    - jaraco-classes==3.2.2
    - jedi==0.18.1
    - jellyfish==0.9.0
    - jinja2==3.1.2
    - jinja2-time==0.2.0
    - joblib==1.1.0
    - jsonschema==4.15.0
    - jupyter==1.0.0
    - jupyter-client==7.3.5
    - jupyter-console==6.4.4
    - jupyter-core==4.11.1
    - jupyterlab-pygments==0.2.2
    - jupyterlab-widgets==3.0.3
    - keras==2.4.3
    - keras-nightly==2.11.0.dev2022091307
    - keras-preprocessing==1.1.2
    - keyring==23.9.1
    - kiwisolver==1.4.4
    - lazy-object-proxy==1.7.1
    - libclang==14.0.6
    - lxml==4.9.1
    - markdown==3.4.1
    - markupsafe==2.1.1
    - matplotlib==3.5.3
    - matplotlib-inline==0.1.6
    - mccabe==0.6.1
    - mistune==2.0.4
    - more-itertools==8.14.0
    - mypy-extensions==0.4.3
    - nbclient==0.6.7
    - nbconvert==7.0.0
    - nbformat==5.4.0
    - nest-asyncio==1.5.5
    - notebook==6.4.12
    - numpy==1.23.3
    - numpydoc==1.4.0
    - oauthlib==3.2.0
    - opt-einsum==3.3.0
    - packaging==21.3
    - pandas==1.4.4
    - pandocfilters==1.5.0
    - parso==0.8.3
    - pathspec==0.10.1
    - pexpect==4.8.0
    - pickleshare==0.7.5
    - pillow==9.2.0
    - pkgutil-resolve-name==1.3.10
    - platformdirs==2.5.2
    - pluggy==1.0.0
    - prometheus-client==0.14.1
    - prompt-toolkit==3.0.31
    - protobuf==3.19.4
    - psutil==5.9.2
    - ptyprocess==0.7.0
    - pure-eval==0.2.2
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pycodestyle==2.8.0
    - pycparser==2.21
    - pydocstyle==6.1.1
    - pydot==1.4.2
    - pydotplus==2.0.2
    - pyflakes==2.4.0
    - pygments==2.13.0
    - pylint==2.15.2
    - pyls-spyder==0.4.0
    - pyobjc-core==8.5
    - pyobjc-framework-cocoa==8.5
    - pyobjc-framework-coreservices==8.5
    - pyobjc-framework-fsevents==8.5
    - pyparsing==3.0.9
    - pyqt5==5.15.7
    - pyqt5-qt5==5.15.2
    - pyqt5-sip==12.11.0
    - pyqtwebengine==5.15.6
    - pyqtwebengine-qt5==5.15.2
    - pyrsistent==0.18.1
    - python-dateutil==2.8.2
    - python-graphviz==0.20.1
    - python-lsp-black==1.2.1
    - python-lsp-jsonrpc==1.0.0
    - python-lsp-server==1.5.0
    - python-slugify==6.1.2
    - pytoolconfig==1.2.2
    - pytz==2022.2.1
    - pyyaml==6.0
    - pyzmq==23.2.1
    - qdarkstyle==3.0.3
    - qstylizer==0.2.2
    - qtawesome==1.1.1
    - qtconsole==5.3.2
    - qtpy==2.2.0
    - requests==2.28.1
    - requests-oauthlib==1.3.1
    - rope==1.3.0
    - rsa==4.9
    - rtree==1.0.0
    - scikit-learn==1.1.2
    - scipy==1.9.1
    - seaborn==0.12.0
    - send2trash==1.8.0
    - six==1.15.0
    - snowballstemmer==2.2.0
    - sortedcontainers==2.4.0
    - soupsieve==2.3.2.post1
    - sphinx==5.1.1
    - sphinxcontrib-applehelp==1.0.2
    - sphinxcontrib-devhelp==1.0.2
    - sphinxcontrib-htmlhelp==2.0.0
    - sphinxcontrib-jsmath==1.0.1
    - sphinxcontrib-qthelp==1.0.3
    - sphinxcontrib-serializinghtml==1.1.5
    - spyder==5.3.3
    - spyder-kernels==2.3.3
    - stack-data==0.5.0
    - tb-nightly==2.11.0a20220913
    - tensorboard==2.10.0
    - tensorboard-data-server==0.6.1
    - tensorboard-plugin-wit==1.8.1
    - tensorflow==2.4.0
    - tensorflow-estimator==2.4.0
    - tensorflow-io-gcs-filesystem==0.27.0
    - tensorflow-probability==0.17.0
    - termcolor==1.1.0
    - terminado==0.15.0
    - text-unidecode==1.3
    - textdistance==4.4.0
    - tf-estimator-nightly==2.11.0.dev2022091308
    - tf-nightly==2.11.0.dev20220913
    - threadpoolctl==3.1.0
    - three-merge==0.1.1
    - tinycss2==1.1.1
    - toml==0.10.2
    - tomli==2.0.1
    - tomlkit==0.11.4
    - torch==1.9.0
    - tornado==6.2
    - tqdm==4.64.1
    - traitlets==5.3.0
    - typing-extensions==4.3.0
    - ujson==5.4.0
    - urllib3==1.26.12
    - watchdog==2.1.9
    - wcwidth==0.2.5
    - webencodings==0.5.1
    - werkzeug==2.2.2
    - whatthepatch==1.0.2
    - widgetsnbextension==4.0.3
    - wrapt==1.12.1
    - wurlitzer==3.0.2
    - yapf==0.32.0
    - zipp==3.8.1
